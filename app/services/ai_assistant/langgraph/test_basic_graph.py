"""
基础图测试

测试LangGraph的基本功能，验证状态传递和节点执行。
"""

import asyncio
import logging
from typing import Dict, Any
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import HumanMessage, AIMessage

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState
from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils
from app.services.ai_assistant.langgraph.adapters.state_adapter import StateAdapter

logger = logging.getLogger(__name__)

async def simple_router_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """简单路由节点"""
    try:
        # 获取用户消息
        user_message = ""
        messages = state.get("messages", [])
        if messages:
            for msg in reversed(messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break
        
        # 简单路由决策
        if "健身" in user_message or "训练" in user_message:
            route = "enhanced"
            confidence = 0.9
        else:
            route = "state_machine"
            confidence = 0.7
        
        # 更新状态
        state["current_node"] = "router"
        state["routing_decision"] = {
            "route": route,
            "confidence": confidence,
            "reasoning": f"基于关键词分析选择{route}路由"
        }
        
        logger.info(f"路由决策: {route} (置信度: {confidence:.2f})")
        return state
        
    except Exception as e:
        logger.error(f"路由节点失败: {str(e)}")
        state["current_node"] = "router"
        state["routing_decision"] = {
            "route": "state_machine",
            "confidence": 0.5,
            "reasoning": f"路由失败，使用默认: {str(e)}"
        }
        return state

async def simple_enhanced_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """简单增强处理节点"""
    try:
        response_content = "增强处理器：我是您的专业健身AI助手，很高兴为您提供健身指导！"
        
        # 更新状态
        state["current_node"] = "enhanced"
        state["processing_system"] = "enhanced"
        state["response_content"] = response_content
        state["confidence"] = 0.9
        state["intent"] = "fitness_guidance"
        
        # 添加AI响应
        ai_message = AIMessage(content=response_content)
        state["messages"].append(ai_message)
        
        logger.info("增强处理完成")
        return state
        
    except Exception as e:
        logger.error(f"增强处理失败: {str(e)}")
        error_response = "抱歉，增强处理器暂时无法处理您的请求。"
        state["current_node"] = "enhanced"
        state["processing_system"] = "enhanced"
        state["response_content"] = error_response
        StateUtils.set_error(state, f"增强处理失败: {str(e)}")
        
        ai_message = AIMessage(content=error_response)
        state["messages"].append(ai_message)
        
        return state

async def simple_state_machine_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """简单状态机处理节点"""
    try:
        response_content = "状态机处理器：您好！我是您的健身助手，请告诉我您需要什么帮助。"
        
        # 更新状态
        state["current_node"] = "state_machine"
        state["processing_system"] = "state_machine"
        state["response_content"] = response_content
        state["confidence"] = 0.8
        state["intent"] = "general_chat"
        
        # 添加AI响应
        ai_message = AIMessage(content=response_content)
        state["messages"].append(ai_message)
        
        logger.info("状态机处理完成")
        return state
        
    except Exception as e:
        logger.error(f"状态机处理失败: {str(e)}")
        error_response = "抱歉，状态机处理器暂时无法处理您的请求。"
        state["current_node"] = "state_machine"
        state["processing_system"] = "state_machine"
        state["response_content"] = error_response
        StateUtils.set_error(state, f"状态机处理失败: {str(e)}")
        
        ai_message = AIMessage(content=error_response)
        state["messages"].append(ai_message)
        
        return state

def route_to_processor(state: UnifiedFitnessState) -> str:
    """路由条件函数"""
    try:
        routing_decision = state.get("routing_decision", {})
        route = routing_decision.get("route", "state_machine")
        
        # 验证路由
        valid_routes = ["enhanced", "state_machine"]
        if route not in valid_routes:
            logger.warning(f"无效路由: {route}，使用默认")
            return "state_machine"
        
        logger.info(f"路由到: {route}")
        return route
        
    except Exception as e:
        logger.error(f"路由条件失败: {str(e)}")
        return "state_machine"

class BasicTestGraph:
    """基础测试图"""
    
    def __init__(self):
        self.graph = None
        self.compiled_graph = None
        self.checkpointer = None
        self._initialized = False
    
    def initialize(self) -> bool:
        """初始化图"""
        try:
            # 创建状态图
            self.graph = StateGraph(UnifiedFitnessState)
            
            # 添加节点
            self.graph.add_node("router", simple_router_node)
            self.graph.add_node("enhanced", simple_enhanced_node)
            self.graph.add_node("state_machine", simple_state_machine_node)
            
            # 添加边
            self.graph.set_entry_point("router")
            self.graph.add_conditional_edges(
                "router",
                route_to_processor,
                {
                    "enhanced": "enhanced",
                    "state_machine": "state_machine"
                }
            )
            self.graph.add_edge("enhanced", END)
            self.graph.add_edge("state_machine", END)
            
            # 使用内存检查点
            self.checkpointer = MemorySaver()
            
            # 编译图
            self.compiled_graph = self.graph.compile(checkpointer=self.checkpointer)
            
            self._initialized = True
            logger.info("基础测试图初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"基础测试图初始化失败: {str(e)}")
            return False
    
    async def process_message(self, message: str, conversation_id: str, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """处理消息"""
        try:
            if not self._initialized:
                if not self.initialize():
                    raise Exception("图初始化失败")
            
            # 创建初始状态
            initial_state = StateAdapter.to_unified_state(
                message=message,
                conversation_id=conversation_id,
                user_info=user_info
            )
            
            # 配置
            config = {
                "configurable": {
                    "thread_id": conversation_id,
                    "checkpoint_ns": "basic_test"
                }
            }
            
            # 执行图
            result = await self.compiled_graph.ainvoke(initial_state, config=config)
            
            # 转换为API响应
            api_response = StateAdapter.create_api_response(result)
            
            logger.info(f"基础图执行完成: {conversation_id}")
            return api_response
            
        except Exception as e:
            logger.error(f"基础图执行失败: {str(e)}")
            return {
                "success": False,
                "response": f"处理失败: {str(e)}",
                "intent_type": "error",
                "confidence": 0.0,
                "conversation_id": conversation_id,
                "error": str(e)
            }

# 全局实例
basic_test_graph = BasicTestGraph()

async def test_basic_graph_functionality():
    """测试基础图功能"""
    try:
        print("开始测试基础图功能...")
        
        # 测试健身相关消息
        result1 = await basic_test_graph.process_message(
            message="你好，我想了解健身训练",
            conversation_id="test_conv_1",
            user_info={"user_id": "test_user", "nickname": "测试用户"}
        )
        
        print("测试1 - 健身消息:")
        print(f"  成功: {result1.get('success', False)}")
        print(f"  响应: {result1.get('response', '')[:100]}...")
        print(f"  意图: {result1.get('intent_type', 'unknown')}")
        print(f"  置信度: {result1.get('confidence', 0.0):.2f}")
        print(f"  处理系统: {result1.get('processing_info', {}).get('system', 'unknown')}")
        
        # 测试一般消息
        result2 = await basic_test_graph.process_message(
            message="你好，今天天气怎么样？",
            conversation_id="test_conv_2",
            user_info={"user_id": "test_user", "nickname": "测试用户"}
        )
        
        print("\n测试2 - 一般消息:")
        print(f"  成功: {result2.get('success', False)}")
        print(f"  响应: {result2.get('response', '')[:100]}...")
        print(f"  意图: {result2.get('intent_type', 'unknown')}")
        print(f"  置信度: {result2.get('confidence', 0.0):.2f}")
        print(f"  处理系统: {result2.get('processing_info', {}).get('system', 'unknown')}")
        
        # 检查结果
        success = (
            result1.get('success', False) and 
            result2.get('success', False) and
            result1.get('processing_info', {}).get('system') == 'enhanced' and
            result2.get('processing_info', {}).get('system') == 'state_machine'
        )
        
        print(f"\n✅ 基础图测试{'成功' if success else '失败'}")
        return success
        
    except Exception as e:
        print(f"❌ 基础图测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_basic_graph_functionality())
