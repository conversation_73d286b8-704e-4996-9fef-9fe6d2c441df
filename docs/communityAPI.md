# 社区功能 API 文档

## 🎉 V3 模块化重构版 (2024年最新)

### 重构亮点
- ✅ **模块化架构**: 将原来的单一文件拆分为6个功能模块
- ✅ **新增功能**: 新增5个实用的API接口
- ✅ **向后兼容**: 保持100%向后兼容性
- ✅ **37个路由**: 完整的社区功能API接口

### 新增API接口
1. **`GET /users/{user_id}/follow-status/`** - 检查关注状态
2. **`POST /comments/{comment_id}/replies/`** - 创建回复
3. **`GET /comments/{comment_id}/replies/`** - 获取回复列表
4. **`DELETE /comments/{comment_id}/like/`** - 取消点赞评论
5. **`GET /users/{user_id}/stats/`** - 用户统计信息

---

## 目录
1. [概述](#概述)
2. [数据模型](#数据模型)
3. [API 接口](#api-接口)
4. [新增接口详情](#新增接口详情)
5. [数据流程](#数据流程)
6. [错误处理](#错误处理)

## 概述

社区功能为用户提供了一个互动平台，允许用户分享训练计划、发布帖子、评论互动、关注其他用户等。主要功能包括：

- 每日训练分享
- 帖子发布与管理
- 评论系统
- 点赞功能
- 用户关注系统
- 通知系统
- 图片管理

## 数据模型

### 1. 每日训练 (DailyWorkout)
```python
class DailyWorkout(Base):
    __tablename__ = "daily_workouts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    title = Column(String(100), nullable=False)
    content = Column(String(1000), nullable=False)
    status = Column(Enum(DailyWorkoutStatus), default=DailyWorkoutStatus.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

### 2. 帖子 (Post)
```python
class Post(Base):
    __tablename__ = "posts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    title = Column(String(100), nullable=False)
    content = Column(String(5000), nullable=False)
    related_workout_id = Column(Integer, ForeignKey("daily_workouts.id"), nullable=True)
    like_count = Column(Integer, default=0)
    comment_count = Column(Integer, default=0)
    share_count = Column(Integer, default=0)
    status = Column(Enum(PostStatus), default=PostStatus.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

### 3. 评论 (Comment)
```python
class Comment(Base):
    __tablename__ = "comments"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    post_id = Column(Integer, ForeignKey("posts.id"), nullable=False)
    parent_id = Column(Integer, ForeignKey("comments.id"), nullable=True)
    content = Column(String(1000), nullable=False)
    like_count = Column(Integer, default=0)
    status = Column(Enum(CommentStatus), default=CommentStatus.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

### 4. 通知 (Notification)
```python
class Notification(Base):
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    type = Column(Enum(NotificationType), nullable=False)
    content = Column(String(500), nullable=False)
    is_read = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
```

### 5. 用户关系 (UserRelation)
```python
class UserRelation(Base):
    __tablename__ = "user_relations"

    id = Column(Integer, primary_key=True, index=True)
    follower_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    following_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
```

### 6. 图片 (Image)
```python
class Image(Base):
    __tablename__ = "images"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    post_id = Column(Integer, ForeignKey("posts.id"), nullable=True)
    url = Column(String(500), nullable=False)
    title = Column(String(100), nullable=True)
    description = Column(String(500), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

## API 接口

### 1. 每日训练接口

#### 创建每日训练
```http
POST /api/v1/community/daily-workouts/
Content-Type: application/json

{
    "title": "今日训练计划",
    "content": "训练内容...",
    "exercises": [
        {
            "exercise_id": 1,
            "sets": 3,
            "reps": "12-15",
            "rest_seconds": 60
        }
    ]
}
```

#### 更新每日训练
```http
PUT /api/v1/community/daily-workouts/{workout_id}
Content-Type: application/json

{
    "title": "更新后的训练计划",
    "content": "更新后的内容..."
}
```

#### 获取每日训练列表
```http
GET /api/v1/community/daily-workouts/?skip=0&limit=20
```

#### 搜索每日训练
```http
GET /api/v1/community/daily-workouts/search/?keyword=训练&skip=0&limit=20
```

### 2. 帖子接口

#### 创建帖子
```http
POST /api/v1/community/posts/
Content-Type: application/json

{
    "title": "我的训练心得",
    "content": "帖子内容...",
    "related_workout_id": 1,
    "image_urls": ["url1", "url2"]
}
```

#### 更新帖子
```http
PUT /api/v1/community/posts/{post_id}
Content-Type: application/json

{
    "title": "更新后的标题",
    "content": "更新后的内容..."
}
```

#### 获取帖子列表
```http
GET /api/v1/community/posts/?skip=0&limit=20
```

#### 点赞帖子
```http
POST /api/v1/community/posts/{post_id}/like/
```

#### 举报帖子
```http
POST /api/v1/community/posts/{post_id}/report/
Content-Type: application/json

{
    "reason": "违规内容"
}
```

### 3. 评论接口

#### 创建评论
```http
POST /api/v1/community/posts/{post_id}/comments/
Content-Type: application/json

{
    "content": "评论内容",
    "parent_id": null
}
```

#### 更新评论
```http
PUT /api/v1/community/comments/{comment_id}
Content-Type: application/json

{
    "content": "更新后的评论内容"
}
```

#### 点赞评论
```http
POST /api/v1/community/comments/{comment_id}/like/
```

#### 举报评论
```http
POST /api/v1/community/comments/{comment_id}/report/
Content-Type: application/json

{
    "reason": "违规内容"
}
```

### 4. 通知接口

#### 获取通知列表
```http
GET /api/v1/community/notifications/?skip=0&limit=20
```

#### 标记通知为已读
```http
PATCH /api/v1/community/notifications/{notification_id}/read/
```

#### 标记所有通知为已读
```http
PATCH /api/v1/community/notifications/read-all/
```

### 5. 用户关系接口

#### 关注用户
```http
POST /api/v1/community/users/{user_id}/follow/
```

#### 取消关注
```http
DELETE /api/v1/community/users/{user_id}/follow/
```

#### 获取关注列表
```http
GET /api/v1/community/users/{user_id}/following/?skip=0&limit=20
```

#### 获取粉丝列表
```http
GET /api/v1/community/users/{user_id}/followers/?skip=0&limit=20
```

### 6. 图片接口

#### 上传图片
```http
POST /api/v1/community/images/
Content-Type: multipart/form-data

{
    "file": "图片文件",
    "title": "图片标题",
    "description": "图片描述",
    "post_id": 1
}
```

#### 更新图片信息
```http
PUT /api/v1/community/images/{image_id}
Content-Type: application/json

{
    "title": "新标题",
    "description": "新描述"
}
```

## 新增接口详情

### 1. 用户关注状态检查

#### 检查关注状态
```http
GET /api/v1/community/users/{user_id}/follow-status/
Authorization: Bearer {token}
```

**响应示例:**
```json
{
    "user_id": 123,
    "is_following": true,
    "following_count": 45,
    "followers_count": 128,
    "followed_at": "2024-01-15T10:30:00Z"
}
```

#### 获取用户统计信息
```http
GET /api/v1/community/users/{user_id}/stats/
```

**响应示例:**
```json
{
    "user_id": 123,
    "following_count": 45,
    "followers_count": 128,
    "posts_count": 32,
    "comments_count": 156,
    "total_likes_received": 892,
    "user_info": {
        "id": 123,
        "nickname": "健身达人",
        "avatar_url": "https://example.com/avatar.jpg",
        "created_at": "2023-06-01T08:00:00Z"
    }
}
```

### 2. 评论回复系统

#### 创建回复
```http
POST /api/v1/community/comments/{comment_id}/replies/
Content-Type: application/json
Authorization: Bearer {token}

{
    "content": "这是对评论的回复内容"
}
```

#### 获取评论回复列表
```http
GET /api/v1/community/comments/{comment_id}/replies/
```

**响应示例:**
```json
{
    "total": 5,
    "items": [
        {
            "id": 456,
            "content": "回复内容",
            "user_id": 789,
            "post_id": 123,
            "parent_id": 321,
            "status": "ACTIVE",
            "created_at": "2024-01-15T10:30:00Z",
            "updated_at": "2024-01-15T10:30:00Z",
            "like_count": 3,
            "is_liked_by_current_user": false,
            "user": {
                "id": 789,
                "nickname": "用户昵称",
                "avatar_url": "https://example.com/avatar.jpg"
            },
            "replies": []
        }
    ]
}
```

### 3. 评论点赞优化

#### 取消点赞评论
```http
DELETE /api/v1/community/comments/{comment_id}/like/
Authorization: Bearer {token}
```

**响应示例:**
```json
{
    "status": "unliked",
    "message": "取消点赞成功"
}
```

### 4. 帖子详情增强

#### 获取帖子详情（增强版）
```http
GET /api/v1/community/posts/{post_id}
Authorization: Bearer {token}
```

**响应示例（用户信息中新增 is_following 字段）:**
```json
{
    "id": 123,
    "title": "我的训练心得",
    "content": "帖子内容...",
    "user": {
        "id": 456,
        "nickname": "健身达人",
        "avatar_url": "https://example.com/avatar.jpg",
        "is_following": true
    },
    "like_count": 25,
    "comment_count": 8,
    "created_at": "2024-01-15T10:30:00Z",
    "comments": [
        {
            "id": 789,
            "content": "评论内容",
            "user": {
                "id": 101,
                "nickname": "评论者",
                "avatar_url": "https://example.com/avatar2.jpg"
            },
            "replies": [
                {
                    "id": 790,
                    "content": "回复内容",
                    "user": {
                        "id": 102,
                        "nickname": "回复者",
                        "avatar_url": "https://example.com/avatar3.jpg"
                    }
                }
            ]
        }
    ]
}
```

### 5. 训练分享功能

#### 分享训练到社区
```http
POST /api/v1/community/workout/{workout_id}/share
Content-Type: application/json
Authorization: Bearer {token}

{
    "title": "今日训练记录",
    "content": "完成了一次高强度训练",
    "workout_data": {
        "duration": 60,
        "exercises": [
            {
                "name": "深蹲",
                "sets": 3,
                "reps": 12,
                "weight": 80
            }
        ]
    },
    "visibility": "public",
    "tags": ["力量训练", "腿部"],
    "images": ["image_url1", "image_url2"]
}
```

**响应示例:**
```json
{
    "id": 123,
    "title": "今日训练记录",
    "content": "完成了一次高强度训练",
    "user_id": 456,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z",
    "tags": ["力量训练", "腿部"],
    "message": "训练分享成功"
}
```

## 数据流程

### 1. 帖子发布流程
1. 用户创建帖子
2. 系统验证用户权限和内容
3. 保存帖子到数据库
4. 更新用户帖子计数
5. 发送通知给关注者

### 2. 评论流程
1. 用户发表评论
2. 系统验证评论内容
3. 保存评论到数据库
4. 更新帖子评论计数
5. 发送通知给帖子作者

### 3. 点赞流程
1. 用户点赞
2. 系统检查是否已点赞
3. 更新点赞计数
4. 发送通知给被点赞者

### 4. 关注流程
1. 用户关注其他用户
2. 系统检查是否已关注
3. 创建关注关系
4. 发送通知给被关注者

## 错误处理

### 通用错误码
- 400: 请求参数错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 500: 服务器内部错误

### 业务错误码
- 1001: 内容违规
- 1002: 重复操作
- 1003: 资源不存在
- 1004: 权限不足
- 1005: 操作失败

### 错误响应格式
```json
{
    "code": 1001,
    "message": "内容包含违规信息",
    "detail": "具体错误详情"
}
```

---

## 🏗️ 模块化架构说明

### 文件结构
```
app/api/endpoints/community/
├── __init__.py              # 主路由聚合器 (20行)
├── posts.py                 # 帖子相关接口 (185行)
├── comments.py              # 评论和回复相关接口 (165行)
├── users.py                 # 用户关注相关接口 (140行)
├── notifications.py         # 通知相关接口 (65行)
├── images.py                # 图片相关接口 (45行)
└── workouts.py             # 训练分享相关接口 (147行)
```

### 路由分布统计

#### 帖子相关 (posts.py) - 8个路由
- `POST /posts/` - 创建帖子
- `PUT /posts/{post_id}` - 更新帖子
- `DELETE /posts/{post_id}` - 删除帖子
- `GET /posts/` - 获取帖子列表
- `GET /posts/{post_id}` - 获取帖子详情（增强版）⭐
- `GET /posts/search/` - 搜索帖子
- `POST /posts/{post_id}/like/` - 点赞帖子
- `POST /posts/{post_id}/report/` - 举报帖子

#### 评论相关 (comments.py) - 7个路由
- `POST /posts/{post_id}/comments/` - 创建评论
- `POST /comments/{comment_id}/replies/` - 创建回复 ⭐新增
- `GET /comments/{comment_id}/replies/` - 获取回复列表 ⭐新增
- `PUT /comments/{comment_id}` - 更新评论
- `DELETE /comments/{comment_id}` - 删除评论
- `POST /comments/{comment_id}/like/` - 点赞评论
- `DELETE /comments/{comment_id}/like/` - 取消点赞评论 ⭐新增
- `POST /comments/{comment_id}/report/` - 举报评论

#### 用户关注相关 (users.py) - 6个路由
- `POST /users/{user_id}/follow/` - 关注用户
- `DELETE /users/{user_id}/follow/` - 取消关注用户
- `GET /users/{user_id}/follow-status/` - 检查关注状态 ⭐新增
- `GET /users/{user_id}/following/` - 获取关注列表
- `GET /users/{user_id}/followers/` - 获取粉丝列表
- `GET /users/{user_id}/stats/` - 获取用户统计 ⭐新增

#### 通知相关 (notifications.py) - 5个路由
- `GET /notifications/` - 获取通知列表
- `GET /notifications/filter/` - 筛选通知
- `PATCH /notifications/{notification_id}/read/` - 标记已读
- `PATCH /notifications/read-all/` - 全部标记已读
- `DELETE /notifications/{notification_id}` - 删除通知

#### 图片相关 (images.py) - 3个路由
- `POST /images/` - 创建图片记录
- `PUT /images/{image_id}` - 更新图片记录
- `DELETE /images/{image_id}` - 删除图片记录

#### 训练分享相关 (workouts.py) - 7个路由
- `POST /workout/{workout_id}/share` - 分享训练
- `POST /daily-workouts/` - 创建单日训练
- `PUT /daily-workouts/{workout_id}` - 更新单日训练
- `DELETE /daily-workouts/{workout_id}` - 删除单日训练
- `GET /daily-workouts/{workout_id}` - 获取单日训练详情
- `GET /daily-workouts/` - 获取单日训练列表
- `GET /daily-workouts/search/` - 搜索单日训练

### 技术优势

#### 1. 模块化设计
- **职责分离**: 每个模块专注于特定功能领域
- **代码复用**: 减少重复代码，提高开发效率
- **易于维护**: 修改某个功能不会影响其他模块

#### 2. 可扩展性
- **新功能添加**: 可以轻松添加新的功能模块
- **独立开发**: 支持多人并行开发不同模块
- **版本控制**: 便于代码版本管理和冲突解决

#### 3. 测试友好
- **单元测试**: 每个模块可以独立进行单元测试
- **集成测试**: 模块间的集成测试更加清晰
- **调试便利**: 问题定位更加精确

#### 4. 性能优化
- **按需加载**: 可以根据需要加载特定模块
- **缓存策略**: 不同模块可以采用不同的缓存策略
- **监控细化**: 可以对每个模块进行细化监控

### 向后兼容性保证
- ✅ **API路径不变**: 所有原有API路径保持不变
- ✅ **响应格式一致**: 响应数据格式完全兼容
- ✅ **功能增强**: 在原有功能基础上增加新特性
- ✅ **无缝升级**: 客户端无需修改即可使用新功能

### 部署建议
1. **渐进式部署**: 可以逐个模块进行部署和测试
2. **回滚策略**: 如有问题可以快速回滚到原版本
3. **监控加强**: 部署后加强API监控和日志记录
4. **性能测试**: 验证重构后的性能表现

---

**📚 更多信息**: 详细的重构文档请参考 `docs/community_refactor_summary.md`