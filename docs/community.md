

**目标：** 构建一个用户社区，允许用户发布帖子、评论帖子、点赞帖子和评论。

**实施步骤:**

1.  **数据模型 (`app/models/`)**
    *   需要创建新的 SQLAlchemy 模型来表示帖子 (`Post`)、评论 (`Comment`) 和点赞 (`Like`)。
    *   这些模型将包含用户ID (`user_id`) 作为外键，关联到现有的 `User` 模型。

2.  **Pydantic Schemas (`app/schemas/`)**
    *   创建对应的 Pydantic Schemas 用于 API 的请求和响应验证。
    *   包括创建 (`Create`)、更新 (`Update`) 和读取 (`Read`) 操作的 Schemas。
    *   `Read` Schemas 应包含关联的用户信息（例如发帖人、评论人）以及动态计算的字段,can（如点赞数、评论数、当前用户是否已点赞）。

3.  **CRUD 操作 (`app/crud/`)**
    *   为 `Post`、`Comment` 和 `Like` 模型创建相应的 CRUD 函数。
    *   这些函数将封装基本的数据库交互（创建、读取、更新、删除）。
    *   需要实现高效的查询，例如分页获取帖子列表、获取某帖子的评论列表、查询用户是否点赞等。

4.  **业务逻辑服务 (`app/services/`)**
    *   创建一个新的服务 `community_service.py` 来处理社区功能的业务逻辑。
    *   该服务将协调 CRUD 操作，处理权限检查（例如，用户只能编辑/删除自己的帖子/评论），计算点赞/评论数，并准备最终返回给 API 层的数据。

5.  **API 端点 (`app/api/endpoints/`)**
    *   创建一个新的 API 路由文件 `community.py`。
    *   定义用于创建、获取、更新、删除帖子和评论，以及点赞/取消点赞的 API 端点。
    *   使用 FastAPI 的 `APIRouter`，并通过依赖注入 (`Depends`) 获取数据库会话和当前用户信息。
    *   应用 Pydantic Schemas 进行请求体验证和响应模型定义。

6.  **数据库迁移 (`alembic/`)**
    *   在定义完模型后，使用 Alembic 生成数据库迁移脚本，以在数据库中创建新表。

7.  **API 路由集成**
    *   在 `app/api/v1/api.py` 中导入并包含新的社区 API 路由。

**详细设计:**

**1. 数据模型 (`app/models/`)**

   *   **`app/models/community_post.py`**:
        ```python
        import sqlalchemy as sa
        from sqlalchemy.orm import relationship
        from sqlalchemy.sql import func

        from app.db.base_class import Base
        from .user import User # Assuming User model is in user.py

        class Post(Base):
            id = sa.Column(sa.Integer, primary_key=True, index=True)
            content = sa.Column(sa.Text, nullable=False)
            image_urls = sa.Column(sa.JSON) # Optional: Store list of image URLs

            user_id = sa.Column(sa.Integer, sa.ForeignKey("user.id"), nullable=False, index=True)
            user = relationship("User", back_populates="posts") # Add 'posts' relationship to User model

            created_at = sa.Column(sa.DateTime(timezone=True), server_default=func.now())
            updated_at = sa.Column(sa.DateTime(timezone=True), onupdate=func.now())

            comments = relationship("Comment", back_populates="post", cascade="all, delete-orphan")
            likes = relationship("PostLike", back_populates="post", cascade="all, delete-orphan")

            # Add back_populates to User model:
            # posts = relationship("Post", back_populates="user")
        ```
   *   **`app/models/community_comment.py`**:
        ```python
        import sqlalchemy as sa
        from sqlalchemy.orm import relationship
        from sqlalchemy.sql import func

        from app.db.base_class import Base
        from .user import User
        from .community_post import Post # Use the correct import path

        class Comment(Base):
            id = sa.Column(sa.Integer, primary_key=True, index=True)
            content = sa.Column(sa.Text, nullable=False)

            user_id = sa.Column(sa.Integer, sa.ForeignKey("user.id"), nullable=False, index=True)
            user = relationship("User", back_populates="comments") # Add 'comments' relationship to User model

            post_id = sa.Column(sa.Integer, sa.ForeignKey("post.id"), nullable=False, index=True)
            post = relationship("Post", back_populates="comments")

            parent_comment_id = sa.Column(sa.Integer, sa.ForeignKey("comment.id"), nullable=True, index=True) # For nested comments
            parent_comment = relationship("Comment", remote_side=[id], backref="replies") # Relationship for replies

            created_at = sa.Column(sa.DateTime(timezone=True), server_default=func.now())
            updated_at = sa.Column(sa.DateTime(timezone=True), onupdate=func.now())

            likes = relationship("CommentLike", back_populates="comment", cascade="all, delete-orphan")

            # Add back_populates to User model:
            # comments = relationship("Comment", back_populates="user")
        ```
   *   **`app/models/community_like.py`**: (Separate tables for post/comment likes for clarity and potential different logic later)
        ```python
        import sqlalchemy as sa
        from sqlalchemy.orm import relationship
        from sqlalchemy.sql import func

        from app.db.base_class import Base
        from .user import User
        from .community_post import Post
        from .community_comment import Comment

        class PostLike(Base):
            id = sa.Column(sa.Integer, primary_key=True, index=True)

            user_id = sa.Column(sa.Integer, sa.ForeignKey("user.id"), nullable=False, index=True)
            user = relationship("User", back_populates="post_likes") # Add 'post_likes' to User

            post_id = sa.Column(sa.Integer, sa.ForeignKey("post.id"), nullable=False, index=True)
            post = relationship("Post", back_populates="likes")

            created_at = sa.Column(sa.DateTime(timezone=True), server_default=func.now())

            # Unique constraint to prevent duplicate likes
            __table_args__ = (sa.UniqueConstraint('user_id', 'post_id', name='_user_post_like_uc'),)

            # Add back_populates to User model:
            # post_likes = relationship("PostLike", back_populates="user")


        class CommentLike(Base):
            id = sa.Column(sa.Integer, primary_key=True, index=True)

            user_id = sa.Column(sa.Integer, sa.ForeignKey("user.id"), nullable=False, index=True)
            user = relationship("User", back_populates="comment_likes") # Add 'comment_likes' to User

            comment_id = sa.Column(sa.Integer, sa.ForeignKey("comment.id"), nullable=False, index=True)
            comment = relationship("Comment", back_populates="likes")

            created_at = sa.Column(sa.DateTime(timezone=True), server_default=func.now())

            # Unique constraint to prevent duplicate likes
            __table_args__ = (sa.UniqueConstraint('user_id', 'comment_id', name='_user_comment_like_uc'),)

             # Add back_populates to User model:
            # comment_likes = relationship("CommentLike", back_populates="user")
        ```
   *   **`app/models/__init__.py`**: Import the new models.
   *   **`app/models/user.py`**: Add the `back_populates` relationships (`posts`, `comments`, `post_likes`, `comment_likes`).

**2. Pydantic Schemas (`app/schemas/`)**

   *   **`app/schemas/community.py`**:
        ```python
        from pydantic import BaseModel, Field
        from typing import List, Optional
        from datetime import datetime

        # Assuming a UserBase/UserRead schema exists in app.schemas.user
        from .user import UserRead # Adjust import as necessary

        # --- Base Schemas ---
        class PostBase(BaseModel):
            content: str = Field(..., min_length=1)
            image_urls: Optional[List[str]] = None

        class CommentBase(BaseModel):
            content: str = Field(..., min_length=1)

        # --- Create Schemas ---
        class PostCreate(PostBase):
            pass

        class CommentCreate(CommentBase):
            post_id: int
            parent_comment_id: Optional[int] = None

        # --- Update Schemas ---
        class PostUpdate(PostBase):
           pass # Allow updating content and images

        class CommentUpdate(CommentBase):
           pass # Allow updating content

        # --- Read Schemas ---
        class CommentRead(CommentBase):
            id: int
            user: UserRead # Nested user info
            post_id: int
            parent_comment_id: Optional[int]
            created_at: datetime
            updated_at: Optional[datetime]
            like_count: int = 0
            is_liked_by_current_user: bool = False # To be populated by service

            class Config:
                orm_mode = True # Pydantic V1 style, use from_attributes=True for V2

        class PostRead(PostBase):
            id: int
            user: UserRead # Nested user info
            created_at: datetime
            updated_at: Optional[datetime]
            like_count: int = 0
            comment_count: int = 0
            is_liked_by_current_user: bool = False # To be populated by service
            # Optional: include some recent comments
            # recent_comments: List[CommentRead] = []

            class Config:
                orm_mode = True # Pydantic V1 style, use from_attributes=True for V2

        # Optional: Schema for Like actions (though not strictly needed for simple like/unlike)
        class LikeAction(BaseModel):
            target_id: int # Post ID or Comment ID depending on endpoint
        ```
   *   **`app/schemas/__init__.py`**: Import the new schemas.

**3. CRUD 操作 (`app/crud/`)**

   *   **`app/crud/crud_community_post.py`**:
        *   `create(db, *, obj_in, user_id)`
        *   `get(db, id)`
        *   `get_multi(db, *, skip, limit, user_id_filter=None)`: Fetch multiple posts, possibly filtered by user.
        *   `update(db, *, db_obj, obj_in)`
        *   `remove(db, *, id)`
   *   **`app/crud/crud_community_comment.py`**:
        *   `create(db, *, obj_in, user_id, post_id)`
        *   `get(db, id)`
        *   `get_multi_by_post(db, *, post_id, skip, limit, parent_comment_id=None)`: Get comments for a post, potentially filtered for replies.
        *   `update(db, *, db_obj, obj_in)`
        *   `remove(db, *, id)`
        *   `count_by_post(db, *, post_id)`
   *   **`app/crud/crud_community_like.py`**:
        *   `create_post_like(db, *, user_id, post_id)`: Creates a `PostLike` if not exists.
        *   `get_post_like(db, *, user_id, post_id)`
        *   `remove_post_like(db, *, user_id, post_id)`
        *   `count_by_post(db, *, post_id)`
        *   `get_posts_liked_by_user(db, *, user_id, post_ids: List[int])`: Efficiently check which posts a user liked from a list.
        *   `create_comment_like(db, *, user_id, comment_id)`: Creates a `CommentLike` if not exists.
        *   `get_comment_like(db, *, user_id, comment_id)`
        *   `remove_comment_like(db, *, user_id, comment_id)`
        *   `count_by_comment(db, *, comment_id)`
        *   `get_comments_liked_by_user(db, *, user_id, comment_ids: List[int])`: Efficiently check which comments a user liked from a list.
   *   **`app/crud/__init__.py`**: Import the new CRUD objects.

**4. 业务逻辑服务 (`app/services/`)**

   *   **`app/services/community_service.py`**:
        *   `create_post(db, current_user, post_in: PostCreate) -> Post`: Calls `crud_post.create`.
        *   `get_posts(db, current_user: Optional[User], skip: int, limit: int) -> List[PostRead]`: Calls `crud_post.get_multi`. For each post, calculates `like_count` (from `crud_like.count_by_post`), `comment_count` (from `crud_comment.count_by_post`), and checks if `current_user` liked it (using `crud_like.get_posts_liked_by_user`). Returns list of enriched `PostRead` objects.
        *   `get_post(db, current_user: Optional[User], post_id: int) -> Optional[PostRead]`: Calls `crud_post.get`. Enriches like/comment counts and like status similar to `get_posts`.
        *   `update_post(db, current_user, post_id: int, post_in: PostUpdate) -> Optional[Post]`: Fetches post, checks ownership (`post.user_id == current_user.id`), calls `crud_post.update`.
        *   `delete_post(db, current_user, post_id: int) -> bool`: Fetches post, checks ownership or admin role, calls `crud_post.remove`. Returns success status.
        *   `create_comment(db, current_user, comment_in: CommentCreate) -> Comment`: Checks if `post_id` exists, calls `crud_comment.create`.
        *   `get_comments_for_post(db, current_user: Optional[User], post_id: int, skip: int, limit: int) -> List[CommentRead]`: Calls `crud_comment.get_multi_by_post`. Enriches each comment with `like_count` and `is_liked_by_current_user`.
        *   `update_comment(db, current_user, comment_id: int, comment_in: CommentUpdate) -> Optional[Comment]`: Fetches comment, checks ownership, calls `crud_comment.update`.
        *   `delete_comment(db, current_user, comment_id: int) -> bool`: Fetches comment, checks ownership or admin role, calls `crud_comment.remove`. Returns success status.
        *   `like_post(db, current_user, post_id: int) -> bool`: Checks if post exists, calls `crud_like.create_post_like`. Returns success/failure (e.g., already liked).
        *   `unlike_post(db, current_user, post_id: int) -> bool`: Calls `crud_like.remove_post_like`. Returns success/failure.
        *   `like_comment(db, current_user, comment_id: int) -> bool`: Checks if comment exists, calls `crud_like.create_comment_like`.
        *   `unlike_comment(db, current_user, comment_id: int) -> bool`: Calls `crud_like.remove_comment_like`.

**5. API 端点 (`app/api/endpoints/`)**

   *   **`app/api/endpoints/community.py`**:
        ```python
        from fastapi import APIRouter, Depends, HTTPException, status, Query
        from sqlalchemy.orm import Session
        from typing import List, Optional

        from app import schemas, models, crud, services # Adjust imports
        from app.api import deps # Assuming deps.get_db and deps.get_current_user exist

        router = APIRouter()

        # --- Posts ---
        @router.post("/posts/", response_model=schemas.PostRead, status_code=status.HTTP_201_CREATED)
        async def create_post(
            *,
            db: Session = Depends(deps.get_db),
            post_in: schemas.PostCreate,
            current_user: models.User = Depends(deps.get_current_user),
            community_service: services.CommunityService = Depends() # Dependency for service
        ):
            # Service handles creation and returns the Read model directly if needed
            # Or return the raw model and convert here/use response_model
            post = await community_service.create_post(db=db, current_user=current_user, post_in=post_in)
            # Re-fetch with enrichment or enrich within service before returning
            return await community_service.get_post(db=db, current_user=current_user, post_id=post.id)


        @router.get("/posts/", response_model=List[schemas.PostRead])
        async def read_posts(
            db: Session = Depends(deps.get_db),
            skip: int = 0,
            limit: int = Query(default=20, le=100),
            current_user: Optional[models.User] = Depends(deps.get_current_user_optional), # Allow anonymous view?
            community_service: services.CommunityService = Depends()
        ):
            return await community_service.get_posts(db=db, current_user=current_user, skip=skip, limit=limit)

        @router.get("/posts/{post_id}", response_model=schemas.PostRead)
        async def read_post(
            post_id: int,
            db: Session = Depends(deps.get_db),
            current_user: Optional[models.User] = Depends(deps.get_current_user_optional),
            community_service: services.CommunityService = Depends()
        ):
            post = await community_service.get_post(db=db, current_user=current_user, post_id=post_id)
            if not post:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Post not found")
            return post

        @router.put("/posts/{post_id}", response_model=schemas.PostRead)
        async def update_post(
            post_id: int,
            *,
            db: Session = Depends(deps.get_db),
            post_in: schemas.PostUpdate,
            current_user: models.User = Depends(deps.get_current_user),
            community_service: services.CommunityService = Depends()
        ):
            updated_post = await community_service.update_post(db=db, current_user=current_user, post_id=post_id, post_in=post_in)
            if not updated_post:
                 raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Post not found or permission denied")
            # Re-fetch with enrichment
            return await community_service.get_post(db=db, current_user=current_user, post_id=updated_post.id)


        @router.delete("/posts/{post_id}", status_code=status.HTTP_204_NO_CONTENT)
        async def delete_post(
            post_id: int,
            *,
            db: Session = Depends(deps.get_db),
            current_user: models.User = Depends(deps.get_current_user),
            community_service: services.CommunityService = Depends()
        ):
            success = await community_service.delete_post(db=db, current_user=current_user, post_id=post_id)
            if not success:
                 raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Post not found or permission denied")
            return None # No content

        # --- Comments ---
        @router.post("/posts/{post_id}/comments/", response_model=schemas.CommentRead, status_code=status.HTTP_201_CREATED)
        async def create_comment(
            post_id: int, # Can get post_id from path or body
            *,
            db: Session = Depends(deps.get_db),
            comment_in_body: schemas.CommentCreate, # Ensure post_id in body matches path or remove from body
            current_user: models.User = Depends(deps.get_current_user),
            community_service: services.CommunityService = Depends()
        ):
             if comment_in_body.post_id != post_id:
                 raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Post ID mismatch")
             comment = await community_service.create_comment(db=db, current_user=current_user, comment_in=comment_in_body)
             # Enrich before returning
             # ... (fetch like count, is_liked)
             return comment # Or enriched version

        @router.get("/posts/{post_id}/comments/", response_model=List[schemas.CommentRead])
        async def read_comments(
            post_id: int,
            db: Session = Depends(deps.get_db),
            skip: int = 0,
            limit: int = Query(default=50, le=200),
            current_user: Optional[models.User] = Depends(deps.get_current_user_optional),
            community_service: services.CommunityService = Depends()
        ):
             # Check if post exists first? Service can handle this.
             return await community_service.get_comments_for_post(db=db, current_user=current_user, post_id=post_id, skip=skip, limit=limit)

        @router.put("/comments/{comment_id}", response_model=schemas.CommentRead)
        async def update_comment(
             comment_id: int,
             *,
             db: Session = Depends(deps.get_db),
             comment_in: schemas.CommentUpdate,
             current_user: models.User = Depends(deps.get_current_user),
             community_service: services.CommunityService = Depends()
        ):
             # ... similar logic to update_post ...
             pass


        @router.delete("/comments/{comment_id}", status_code=status.HTTP_204_NO_CONTENT)
        async def delete_comment(
             comment_id: int,
             *,
             db: Session = Depends(deps.get_db),
             current_user: models.User = Depends(deps.get_current_user),
             community_service: services.CommunityService = Depends()
        ):
            # ... similar logic to delete_post ...
            pass


        # --- Likes ---
        @router.post("/posts/{post_id}/like", status_code=status.HTTP_204_NO_CONTENT)
        async def like_post(
            post_id: int,
            db: Session = Depends(deps.get_db),
            current_user: models.User = Depends(deps.get_current_user),
            community_service: services.CommunityService = Depends()
        ):
            success = await community_service.like_post(db=db, current_user=current_user, post_id=post_id)
            if not success:
                # Could be 404 Not Found or 409 Conflict (already liked)
                raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="Could not like post")


        @router.delete("/posts/{post_id}/like", status_code=status.HTTP_204_NO_CONTENT)
        async def unlike_post(
            post_id: int,
            db: Session = Depends(deps.get_db),
            current_user: models.User = Depends(deps.get_current_user),
            community_service: services.CommunityService = Depends()
        ):
             success = await community_service.unlike_post(db=db, current_user=current_user, post_id=post_id)
             if not success:
                 raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Like not found")


        @router.post("/comments/{comment_id}/like", status_code=status.HTTP_204_NO_CONTENT)
        async def like_comment(
            comment_id: int,
            db: Session = Depends(deps.get_db),
            current_user: models.User = Depends(deps.get_current_user),
            community_service: services.CommunityService = Depends()
        ):
             # ... similar logic to like_post ...
             pass


        @router.delete("/comments/{comment_id}/like", status_code=status.HTTP_204_NO_CONTENT)
        async def unlike_comment(
            comment_id: int,
            db: Session = Depends(deps.get_db),
            current_user: models.User = Depends(deps.get_current_user),
            community_service: services.CommunityService = Depends()
        ):
             # ... similar logic to unlike_post ...
             pass

        ```

**6. 数据库迁移 (`alembic/`)**

   *   在命令行中运行:
        ```bash
        # Ensure models are imported in app/db/base.py or similar base importer
        alembic revision --autogenerate -m "Add community post, comment, and like tables"
        alembic upgrade head
        ```
   *   检查生成的迁移脚本 (`alembic/versions/xxxx_add_community_tables.py`) 是否正确。

**7. API 路由集成**

   *   在 `app/api/v1/api.py` 中:
        ```python
        from app.api.endpoints import community # Import the new router

        api_router = APIRouter()
        # ... include other routers ...
        api_router.include_router(community.router, prefix="/community", tags=["community"])
        ```

**后续考虑:**

*   **性能:** 为 `user_id`, `post_id`, `comment_id`, `created_at` 添加数据库索引。对于 Feed (`GET /posts/`)，考虑更复杂的排序逻辑（如热度、时间衰减）。
*   **图片处理:** 如果允许上传图片，需要集成图片存储（如本地文件、云存储）和处理逻辑。
*   **通知:** 用户发帖/评论被回复/点赞时，可以添加通知功能。
*   **内容审核:** 添加敏感词过滤或举报功能。
*   **测试:** 为新的 CRUD、Service 和 API 编写单元测试和集成测试。
*   **异步:** 确保所有数据库操作和潜在的外部调用（如图片处理）在 Service 和 API 层都是 `async` 的。CRUD 层本身可以是同步的，FastAPI 会在线程池中运行它们。

这个方案提供了一个完整的社区功能基础框架，你可以根据具体需求进行调整和扩展。


# 用户社区功能实施方案 (V3 - 模块化重构版)

**目标：** 构建一个用户社区，允许用户发布带图片的帖子、评论帖子、点赞、查看帖子、接收通知，并包含基础的内容审核和举报机制。

## 🎉 最新更新 (V3 - 2024年)

### 模块化重构完成
- ✅ **代码重构**: 将原来的单一文件 `community.py`（878行）拆分为6个功能模块
- ✅ **新增功能**: 新增5个实用的API接口
- ✅ **向后兼容**: 保持100%向后兼容性
- ✅ **测试验证**: 完整的功能测试和验证

### 新的文件结构
```
app/api/endpoints/community/
├── __init__.py              # 主路由聚合器
├── posts.py                 # 帖子相关接口
├── comments.py              # 评论和回复相关接口
├── users.py                 # 用户关注相关接口
├── notifications.py         # 通知相关接口
├── images.py                # 图片相关接口
└── workouts.py             # 训练分享相关接口
```

### 新增API接口
1. **`GET /users/{user_id}/follow-status/`** - 检查关注状态
2. **`POST /comments/{comment_id}/replies/`** - 创建回复
3. **`GET /comments/{comment_id}/replies/`** - 获取回复列表
4. **`DELETE /comments/{comment_id}/like/`** - 取消点赞评论
5. **`GET /users/{user_id}/stats/`** - 用户统计信息

### 功能增强
- 📝 **帖子详情增强**: 用户信息中添加 `is_following` 字段
- 💬 **评论系统完善**: 支持独立的回复接口和嵌套回复结构
- 👥 **用户关注优化**: 完整的关注状态检查和统计功能
- 📊 **数据统计**: 用户社区活动统计（关注数、粉丝数、帖子数、评论数、获赞数）

**实施步骤:**

1.  **数据模型 (`app/models/`)**
    *   创建/修改 SQLAlchemy 模型: `Post`, `Comment`, `PostLike`, `CommentLike`, `Notification`, `Report`。
    *   为模型添加 `status` (审核状态), `view_count` (帖子浏览量), `image_urls` (存储相对路径), `reported_count` 等字段。
    *   在 `user_id`, `post_id`, `comment_id`, `created_at`, `status` 等字段上添加数据库索引 (`index=True`)。

2.  **Pydantic Schemas (`app/schemas/`)**
    *   创建/修改 Pydantic Schemas: `PostCreate`, `PostUpdate`, `PostRead`, `CommentCreate`, `CommentUpdate`, `CommentRead`, `NotificationRead`, `ReportCreate`, `ReportRead`, `ModerationAction`。
    *   `Read` Schemas 包含关联信息、计算字段 (`like_count`, `comment_count`, `is_liked_by_current_user`), 以及 `view_count`, `status`。
    *   `Create`/`Update` Schemas 移除 `image_urls`，由 API 层处理文件上传。

3.  **CRUD 操作 (`app/crud/`)**
    *   为 `Post`, `Comment`, `Like`, `Notification`, `Report` 创建/更新 CRUD 函数。
    *   `crud_post`: 添加 `increment_view_count` 方法，更新查询方法以支持按 `status` 过滤。
    *   `crud_comment`: 更新查询方法以支持按 `status` 过滤。
    *   `crud_like`: 基本不变，但点赞操作可能触发通知。
    *   `crud_notification`: 管理通知的增删查改。
    *   `crud_report`: 管理举报记录的增删查改。

4.  **业务逻辑服务 (`app/services/`)**
    *   创建/修改服务: `community_service.py`, `notification_service.py`。
    *   **`community_service.py`**:
        *   所有方法改为 `async def`。
        *   `create_post`, `update_post`: 处理 `List[UploadFile]`，调用工具函数保存图片到 `/data/user/community/`，保存相对路径到 `image_urls`。执行基础内容检查，设置初始 `status`。
        *   `create_comment`: 执行基础内容检查，设置初始 `status`。触发通知 (`await notification_service.notify_comment_created(...)`)。
        *   `get_post`: 调用 `crud_post.increment_view_count`。根据用户角色和 `status` 决定是否返回帖子。
        *   `get_posts`, `get_comments_for_post`: 根据 `status` 过滤（默认只返回 `approved`）。丰富 `Read` 模型数据（点赞数、评论数、是否点赞、浏览量）。
        *   `like_post`, `like_comment`: 触发通知 (`await notification_service.notify_liked(...)`)。
        *   `report_content`: 创建 `Report` 记录，增加帖子/评论的 `reported_count`。
        *   `moderate_content`: (管理员) 更新帖子/评论的 `status`。
    *   **`notification_service.py`**:
        *   `async def create_notification(db, *, user_to_notify_id, type, related_post_id=None, related_comment_id=None, related_user_id=None)`: 创建通知记录。
        *   `async def get_notifications_for_user(db, *, user_id, skip, limit, unread_only=True)`: 获取用户通知列表。
        *   `async def mark_notification_as_read(db, *, notification_id, user_id)`: 标记通知为已读。

5.  **API 端点 (`app/api/endpoints/`)**
    *   创建/修改 API 路由文件 `community.py`, `notifications.py`。
    *   **`community.py`**:
        *   所有端点函数改为 `async def`。
        *   `POST /posts/`, `PUT /posts/{post_id}`: 接收 `content: str = Form(...)`, `images: List[UploadFile] = File(None)`。
        *   `GET /posts/`, `GET /posts/{post_id}`, `GET /posts/{post_id}/comments/`: 返回结果根据 `status` 过滤。
        *   `POST /posts/{post_id}/like`, `DELETE /posts/{post_id}/like`, `POST /comments/{comment_id}/like`, `DELETE /comments/{comment_id}/like`: 触发点赞/取消点赞。
        *   `POST /report/`: (用户) 提交举报，接收 `ReportCreate` schema。
        *   `PUT /posts/{post_id}/moderate`, `PUT /comments/{comment_id}/moderate`: (管理员) 审核帖子/评论，接收 `ModerationAction` schema (`{status: 'approved' | 'rejected'}`).
        *   `GET /community/images/{filename}`: 安全地提供 `/data/user/community/` 下的图片文件。
    *   **`notifications.py`**:
        *   `GET /notifications/`: 获取当前用户的通知列表。
        *   `PATCH /notifications/{notification_id}/read`: 将通知标记为已读。

6.  **数据库迁移 (`alembic/`)**
    *   在修改完模型后，使用 Alembic 生成新的数据库迁移脚本并应用。
        ```bash
        alembic revision --autogenerate -m "Enhance community features with views, images, moderation, notifications"
        alembic upgrade head
        ```

7.  **API 路由集成**
    *   在 `app/api/v1/api.py` 中导入并包含新的 `community` 和 `notifications` API 路由。

**详细设计 (示例片段 - 具体实现需在代码中完成):**

**1. 数据模型 (`app/models/community_post.py`)**
```python
# ... imports ...
from sqlalchemy.dialects.postgresql import JSONB # Use JSONB for better indexing if needed

class Post(Base):
    # ... existing fields ...
    content = sa.Column(sa.Text, nullable=False)
    image_urls = sa.Column(JSONB) # Store list of relative image paths
    user_id = sa.Column(sa.Integer, sa.ForeignKey("user.id"), nullable=False, index=True) # Add index
    # ... relationships ...
    created_at = sa.Column(sa.DateTime(timezone=True), server_default=func.now(), index=True) # Add index
    updated_at = sa.Column(sa.DateTime(timezone=True), onupdate=func.now())
    view_count = sa.Column(sa.Integer, default=0, nullable=False, index=True) # Add index
    status = sa.Column(sa.String, default='pending', nullable=False, index=True) # pending, approved, rejected
    reported_count = sa.Column(sa.Integer, default=0, nullable=False) # For moderation priority

    # ... existing relationships ...
```
*   *类似地修改 `Comment`，添加 `status`, `reported_count` 和索引。*
*   *定义 `Notification` 模型 (user_id, type, content, is_read, related_ids, created_at)。*
*   *定义 `Report` 模型 (reporter_user_id, reported_post_id, reported_comment_id, reason, status, created_at)。*

**2. Pydantic Schemas (`app/schemas/community.py`)**
```python
# ... imports ...
class PostRead(PostBase):
    # ... existing fields ...
    id: int
    user: UserRead
    created_at: datetime
    updated_at: Optional[datetime]
    like_count: int = 0
    comment_count: int = 0
    is_liked_by_current_user: bool = False
    view_count: int = 0
    status: str # Expose status
    image_urls: Optional[List[str]] = [] # List of relative paths or full URLs constructed by service/API

    class Config:
        orm_mode = True

class PostCreate(BaseModel): # No longer inherits PostBase directly if image_urls removed
    content: str = Field(..., min_length=1)
    # image_urls removed, handled by UploadFile in API

# ... other schemas ...
```

**4. Service (`app/services/community_service.py`)**
```python
# ... imports ...
import os
import aiofiles # For async file operations
from fastapi import UploadFile, HTTPException
from typing import List
from app.core.config import settings # Assuming image path stored here
from app.utils.file_utils import save_upload_file # Hypothetical utility

IMAGE_SAVE_DIR = settings.COMMUNITY_IMAGE_DIR # Example: "/data/user/community/"

class CommunityService:
    # ... other methods ...

    async def create_post(self, db: Session, current_user: models.User, content: str, images: Optional[List[UploadFile]]) -> models.Post:
        # 1. Content moderation check (basic filter)
        # moderation_result = self.check_content(content)
        # if moderation_result == 'rejected':
        #    raise HTTPException(status_code=400, detail="Content violates policy")
        initial_status = 'approved' # Or 'pending' based on policy

        # 2. Handle image uploads asynchronously
        saved_image_paths = []
        if images:
            if not os.path.exists(IMAGE_SAVE_DIR):
                 os.makedirs(IMAGE_SAVE_DIR) # Ensure dir exists
            for image in images:
                # Generate secure filename, save file, get relative path
                relative_path = await save_upload_file(upload_file=image, destination_dir=IMAGE_SAVE_DIR)
                saved_image_paths.append(relative_path)

        # 3. Create Post object
        post_data = schemas.PostCreateInternal( # Internal schema might be useful
            content=content,
            image_urls=saved_image_paths,
            user_id=current_user.id,
            status=initial_status
            # view_count defaults to 0
        )
        # Use await if CRUD operations become async
        post = crud.post.create(db=db, obj_in=post_data) # Assuming crud returns the model instance
        return post

    async def get_post(self, db: Session, current_user: Optional[models.User], post_id: int) -> Optional[models.Post]: # Return model, API layer uses schema
        post = crud.post.get(db, id=post_id)
        if not post:
            return None

        # Increment view count - consider race conditions if high traffic expected
        # Use await if CRUD becomes async
        crud.post.increment_view_count(db, id=post_id)

        # Check status for visibility
        if post.status != 'approved':
            # Allow owner or admin to see non-approved posts?
            if not current_user or (post.user_id != current_user.id and not current_user.is_superuser):
                 return None # Or raise 403/404

        # Enriching (like counts etc.) might happen here or in a separate step before schema conversion
        # post.like_count = crud.like.count_by_post(db, post_id=post.id) ...
        # post.is_liked_by_current_user = ...
        return post # Return the raw model or enriched model

    # ... other async service methods ...

```

**5. API Endpoints (`app/api/endpoints/community.py`)**
```python
# ... imports ...
from fastapi import File, UploadFile, Form

# ... router setup ...

@router.post("/posts/", response_model=schemas.PostRead, status_code=status.HTTP_201_CREATED)
async def create_post_endpoint( # Renamed to avoid conflict with service method name
    *,
    db: Session = Depends(deps.get_db),
    content: str = Form(...),
    images: Optional[List[UploadFile]] = File(None), # Accept files
    current_user: models.User = Depends(deps.get_current_user),
    community_service: services.CommunityService = Depends()
):
    try:
        # Call the async service method
        post_model = await community_service.create_post(db=db, current_user=current_user, content=content, images=images)
        # Service method returns the model, fetch enriched data for response
        # This fetch should also handle status visibility implicitly
        enriched_post = await community_service.get_post(db=db, current_user=current_user, post_id=post_model.id)
        if not enriched_post: # Should not happen if creation succeeded and status allows view
             raise HTTPException(status_code=500, detail="Failed to retrieve created post")
        # FastAPI automatically converts the *returned* model to the response_model schema
        return enriched_post
    except HTTPException as e:
        raise e # Re-raise validation/permission errors from service
    except Exception as e:
        # Log error
        raise HTTPException(status_code=500, detail="Internal server error during post creation")


@router.get("/community/images/{filename}")
async def get_community_image(filename: str):
    # Implement secure file serving from IMAGE_SAVE_DIR
    # Use FileResponse, check for path traversal vulnerabilities
    from fastapi.responses import FileResponse
    import os

    file_path = os.path.join(IMAGE_SAVE_DIR, filename)
    # SECURITY: Validate filename to prevent path traversal!
    if not os.path.commonprefix((os.path.realpath(file_path), os.path.realpath(IMAGE_SAVE_DIR))) == os.path.realpath(IMAGE_SAVE_DIR):
         raise HTTPException(status_code=404, detail="File not found")

    if os.path.isfile(file_path):
        return FileResponse(file_path)
    else:
        raise HTTPException(status_code=404, detail="File not found")


# ... other async endpoints ...
```

**后续考虑 (更新):**

*   **性能:** 为 `Post.user_id`, `Post.created_at`, `Post.status`, `Post.view_count`, `Comment.user_id`, `Comment.post_id`, `Comment.created_at`, `Comment.status`, `Notification.user_id`, `Notification.is_read`, `Report.reporter_user_id`, `Report.status` 等添加数据库索引。Feed 查询 (`GET /posts/`) 可能需要复合索引和优化排序逻辑。考虑缓存（如 Redis）常用数据（帖子详情、点赞数）。
*   **图片处理:** 使用 `aiofiles` 进行异步文件 I/O。实现安全的 `save_upload_file` 工具函数，生成唯一文件名，限制文件大小和类型。考虑图片压缩、缩略图生成（可能需要后台任务）。`GET /community/images/{filename}` 端点需要严格的文件名验证和权限检查（如果需要）。
*   **通知:** 实现 `notification_service.py` 和相关 CRUD/API。考虑使用 WebSocket 或 SSE (Server-Sent Events) 推送实时通知，或采用轮询方式。需要设计通知类型 (`comment`, `like`, `reply`, `mention`?)。
*   **内容审核:** 实现更复杂的 `check_content` 逻辑（例如集成第三方审核服务 API）。设计举报处理流程和管理员界面。`status` 字段需要在查询和展示逻辑中一致处理。
*   **测试:** 为所有新功能（图片上传、通知、审核、异步流程）编写单元测试和集成测试。
*   **异步:** 确认 Service 和 API 层所有涉及 I/O（DB、文件、外部 API 调用）的操作都使用 `async/await`。如果使用同步 ORM (SQLAlchemy)，FastAPI 会自动处理线程池，但长时间的文件操作或外部 API 调用应明确使用异步库（如 `httpx`, `aiofiles`）。
*   **后台任务:** 对于图片处理（压缩、缩略图）、发送推送通知、复杂内容审核等耗时操作，应使用 FastAPI 的 `BackgroundTasks` 或 Celery 等任务队列执行，避免阻塞 API 响应。
*   **配置管理:** 将图片存储路径、审核策略、通知设置等放入 `app/core/config.py`。

---

请将这些更新整合到你的 `docs/community.md` 文件中。这个增强版方案覆盖了你提出的各项要求，并为后续开发提供了更详细的指导。

社区功能实现总结
根据您的需求，我已经成功实现了社区功能的后端代码。以下是实现的主要内容和结构：
1. 数据模型
DailyWorkout 模型
基于 Workout 模型，但移除了 training_plan_id、day_of_week 和 day_number 字段
添加了 training_duration（训练时长）和 total_capacity（总容量）字段
文件位置：app/models/daily_workout.py
社区相关模型
Post：帖子模型，包含 title 和 related_workout 字段，related_workout 关联到 DailyWorkout
Comment：评论模型，支持嵌套回复
PostLike/CommentLike：点赞模型
Report：举报模型
Notification：通知模型
文件位置：app/models/community.py
2. 数据库关系
修改了 WorkoutExercise 模型，增加了与 DailyWorkout 的关联
更新了 User 模型，增加了与社区相关模型的关系
创建了数据库迁移文件
3. Pydantic 模式
为所有模型创建了对应的 Pydantic 模式（schemas）
包括基础模式、创建模式、更新模式和响应模式
文件位置：app/schemas/community.py
4. CRUD 操作
为 DailyWorkout 创建了 CRUD 操作
为社区相关模型（Post、Comment、Like 等）创建了 CRUD 操作
文件位置：
app/crud/crud_daily_workout.py
app/crud/crud_community.py
5. 业务逻辑服务
创建了 CommunityService 类处理社区相关的业务逻辑
实现了帖子、评论、点赞、举报和通知等功能
支持图片上传和存储
文件位置：app/services/community_service.py
6. API 端点
创建了完整的 RESTful API 端点
包括创建和获取单日训练、帖子、评论等功能
实现了点赞、举报、通知等交互功能
添加了权限控制，如管理员专用的审核接口
文件位置：app/api/endpoints/community.py
7. 依赖注入
添加了 CommunityService 的依赖注入
添加了可选用户认证的依赖函数
文件位置：app/api/deps.py
8. 路由注册
将社区 API 路由注册到主 API 路由器
文件位置：app/api/v1/api.py
9. 静态文件配置
添加了静态文件目录配置，用于存储社区图片
创建了静态文件目录
文件位置：app/core/config.py
10. 修复的问题
安装了 aiofiles 依赖，用于异步文件操作
修复了 deps.get_current_superuser 不存在的问题，改为使用 deps.get_current_active_superuser
功能特点
单日训练分享：用户可以创建和分享单日训练计划
社区互动：支持发帖、评论、点赞等社交互动
通知系统：当有人评论、回复或点赞时，会发送通知
内容审核：管理员可以审核帖子和评论
举报功能：用户可以举报不适当的内容
图片上传：支持在帖子中上传图片
所有这些功能现在都已经实现，并且可以通过 API 使用。数据库迁移也已经完成，新的表结构已经添加到数据库中。

---

## 🚀 V3 模块化重构更新 (2024年最新)

### 重构概述
为了提高代码的可维护性和可扩展性，我们对社区功能进行了全面的模块化重构：

### 主要改进
1. **模块化架构**: 将原来的单一文件拆分为6个功能模块
2. **新增功能**: 新增5个实用的API接口
3. **向后兼容**: 保持100%向后兼容性
4. **代码质量**: 提高代码可读性和可维护性

### 新的文件结构
```
app/api/endpoints/community/
├── __init__.py              # 主路由聚合器 (20行)
├── posts.py                 # 帖子相关接口 (185行)
├── comments.py              # 评论和回复相关接口 (165行)
├── users.py                 # 用户关注相关接口 (140行)
├── notifications.py         # 通知相关接口 (65行)
├── images.py                # 图片相关接口 (45行)
└── workouts.py             # 训练分享相关接口 (147行)
```

### 新增API接口

#### 1. 用户关注状态检查
- **接口**: `GET /api/v1/community/users/{user_id}/follow-status/`
- **功能**: 检查当前用户对指定用户的关注状态
- **返回**: 关注状态、关注时间、关注数、粉丝数等信息

#### 2. 评论回复独立接口
- **接口**: `POST /api/v1/community/comments/{comment_id}/replies/`
- **功能**: 对指定评论创建回复
- **接口**: `GET /api/v1/community/comments/{comment_id}/replies/`
- **功能**: 获取指定评论的回复列表

#### 3. 评论点赞优化
- **接口**: `DELETE /api/v1/community/comments/{comment_id}/like/`
- **功能**: 取消对评论的点赞

#### 4. 用户社区统计
- **接口**: `GET /api/v1/community/users/{user_id}/stats/`
- **功能**: 获取用户的社区统计信息（关注数、粉丝数、帖子数、评论数、获赞数）

#### 5. 帖子详情增强
- **功能**: 在帖子详情的用户信息中添加 `is_following` 字段
- **说明**: 表示当前用户是否关注帖子作者

### 路由统计
重构后的社区模块包含 **37个路由**：
- 帖子相关: 8个路由
- 评论相关: 7个路由
- 用户关注相关: 6个路由
- 通知相关: 5个路由
- 图片相关: 3个路由
- 训练分享相关: 7个路由

### 测试验证
- ✅ 模块导入测试通过
- ✅ 路由结构测试通过
- ✅ 数据库连接测试通过
- ✅ 服务实例化测试通过
- ✅ API功能测试通过

### 部署说明
- **向后兼容性**: 完全兼容，无需修改客户端代码
- **数据库迁移**: 无需迁移，使用现有数据结构
- **部署风险**: 低风险，已充分测试验证

### 技术优势
1. **模块化设计**: 按功能领域拆分，便于维护
2. **代码复用**: 减少重复代码，提高开发效率
3. **团队协作**: 支持多人并行开发不同模块
4. **功能扩展**: 为未来功能扩展奠定良好基础

详细的重构文档请参考: `docs/community_refactor_summary.md`