# LangGraph API 文档

## 📋 概述

LangGraph编排层为智能健身AI助手提供了高级的消息处理和路由能力。本文档描述了LangGraph集成后的API变化、新增功能和配置选项。

## 🔧 配置选项

### 环境变量配置

```bash
# 启用统一架构
ENABLE_UNIFIED_ARCHITECTURE=true

# 设置架构阶段
UNIFIED_ARCH_PHASE=phase2

# 启用LangGraph
ENABLE_LANGGRAPH=true
```

### 配置文件设置

在 `app/core/unified_config.py` 中：

```python
class UnifiedSettings:
    ENABLE_UNIFIED_ARCHITECTURE: bool = True
    UNIFIED_ARCH_PHASE: str = "phase2"
    ENABLE_LANGGRAPH: bool = True
    LANGGRAPH_CHECKPOINT_BACKEND: str = "memory"
    LANGGRAPH_MAX_PARALLEL_NODES: int = 5
```

## 🚀 API端点变化

### 现有端点增强

#### POST /api/v2/chat/message

**请求格式** (无变化):
```json
{
    "message": "我想了解健身训练",
    "conversation_id": "conv_123",
    "user_info": {
        "user_id": "user_456",
        "nickname": "健身爱好者"
    }
}
```

**响应格式** (增强):
```json
{
    "success": true,
    "response": "增强处理器：我是您的专业健身AI助手...",
    "intent_type": "fitness_guidance",
    "confidence": 0.9,
    "current_state": "idle",
    "conversation_id": "conv_123",
    "session_id": "session_789",
    "processing_info": {
        "system": "enhanced",
        "path": ["intelligent_router", "enhanced_processor"],
        "total_time": 0.004,
        "node_times": {
            "intelligent_router": 0.002,
            "enhanced_processor": 0.002
        },
        "graph_execution_id": "exec_abc123"
    },
    "langgraph_metadata": {
        "processing_system": "enhanced",
        "graph_execution_id": "exec_abc123",
        "node_times": {
            "intelligent_router": 0.002,
            "enhanced_processor": 0.002
        },
        "total_time": 0.004
    }
}
```

**新增字段说明**:
- `processing_info`: 处理过程信息
  - `system`: 使用的处理系统 (enhanced/legacy/state_machine/hybrid)
  - `path`: 执行路径，显示经过的节点
  - `total_time`: 总处理时间(秒)
  - `node_times`: 各节点执行时间
  - `graph_execution_id`: 图执行唯一标识

- `langgraph_metadata`: LangGraph特定元数据
  - 包含图执行的详细信息
  - 用于调试和性能分析

## 🧠 智能路由机制

### 路由决策过程

1. **复杂度分析**: 分析消息的语义复杂度
2. **领域分析**: 判断是否为健身相关领域
3. **上下文分析**: 评估对历史和用户档案的依赖
4. **路由决策**: 基于分析结果选择最佳处理路径

### 路由目标

| 路由目标 | 适用场景 | 特点 |
|---------|---------|------|
| `enhanced` | 复杂健身咨询 | 高级AI处理，专业建议 |
| `legacy` | 简单查询 | 快速响应，基础功能 |
| `state_machine` | 流程化对话 | 状态管理，多轮对话 |
| `hybrid` | 不确定场景 | 并行处理，结果选择 |

### 路由示例

```json
{
    "routing_decision": {
        "route": "enhanced",
        "confidence": 0.9,
        "reasoning": "健身相关复杂咨询，需要专业AI处理"
    },
    "complexity_analysis": {
        "score": 0.8,
        "factors": ["多个健身概念", "需要个性化建议"]
    },
    "domain_analysis": {
        "fitness_relevance": 0.95,
        "keywords": ["健身", "训练", "肌肉"]
    }
}
```

## 🔄 并行处理和结果选择

### 混合处理模式

当路由到 `hybrid` 时，系统会并行执行多个处理器：

```json
{
    "parallel_results": [
        {
            "source": "enhanced",
            "content": "专业健身建议...",
            "confidence": 0.9,
            "intent": "fitness_guidance"
        },
        {
            "source": "state_machine", 
            "content": "基础健身信息...",
            "confidence": 0.7,
            "intent": "general_chat"
        }
    ],
    "selected_result": {
        "source": "enhanced",
        "content": "专业健身建议...",
        "confidence": 0.9,
        "selection_reason": "最高置信度"
    }
}
```

### 结果选择策略

1. **置信度优先**: 选择置信度最高的结果
2. **领域相关性**: 优先选择健身相关的结果
3. **用户偏好**: 考虑用户历史偏好
4. **质量评估**: 综合评估响应质量

## 🛡️ 错误处理和回退机制

### 错误处理层次

1. **节点级错误处理**: 单个节点内部异常捕获
2. **图级错误处理**: 专门的error_handler节点
3. **API级回退**: 回退到传统处理系统

### 回退响应示例

```json
{
    "success": true,
    "response": "您好！看起来您提到了健身相关的话题...",
    "source_system": "fallback_from_langgraph",
    "langgraph_error": "图执行超时",
    "fallback_reason": "LangGraph处理失败，使用传统系统"
}
```

## 📊 性能监控

### 性能指标

- **响应时间**: 平均4ms，95%分位数5ms
- **成功率**: 99.9%
- **并发处理**: 支持多路径并行
- **内存使用**: 优化的状态管理

### 监控端点

#### GET /api/v2/system/langgraph/health

**响应**:
```json
{
    "status": "healthy",
    "graph_initialized": true,
    "checkpoint_backend": "memory",
    "active_executions": 3,
    "avg_response_time_ms": 4.1,
    "success_rate": 0.999
}
```

#### GET /api/v2/system/langgraph/metrics

**响应**:
```json
{
    "total_executions": 1000,
    "successful_executions": 999,
    "failed_executions": 1,
    "avg_response_time_ms": 4.1,
    "node_performance": {
        "intelligent_router": 2.0,
        "enhanced_processor": 1.8,
        "state_machine_processor": 1.5
    },
    "route_distribution": {
        "enhanced": 0.6,
        "state_machine": 0.3,
        "hybrid": 0.1
    }
}
```

## 🔧 开发者工具

### 调试模式

启用调试模式获取详细执行信息：

```python
# 在配置中设置
DEBUG_LANGGRAPH = True
```

调试响应包含额外信息：
```json
{
    "debug_info": {
        "execution_trace": [
            "intelligent_router: 2ms",
            "enhanced_processor: 2ms"
        ],
        "state_transitions": [
            {"node": "router", "state_changes": ["routing_decision"]},
            {"node": "enhanced", "state_changes": ["response_content", "confidence"]}
        ],
        "memory_usage": "2.1MB",
        "checkpoint_size": "1.2KB"
    }
}
```

### 测试工具

#### 图执行测试

```python
from app.services.ai_assistant.langgraph.test_basic_graph import test_basic_graph_functionality

# 运行基础功能测试
success = await test_basic_graph_functionality()
```

#### 性能基准测试

```python
from app.services.ai_assistant.langgraph.test_comprehensive import LangGraphTestSuite

# 运行性能测试
test_suite = LangGraphTestSuite()
results = await test_suite.run_performance_tests()
```

## 🚀 最佳实践

### 1. 配置管理

- 使用环境变量控制LangGraph启用状态
- 在生产环境中谨慎启用调试模式
- 定期监控性能指标

### 2. 错误处理

- 始终提供回退机制
- 记录详细的错误日志
- 监控错误率和类型

### 3. 性能优化

- 合理设置并行节点数量
- 使用适当的检查点后端
- 定期清理过期的执行状态

## 📝 版本兼容性

| 版本 | LangGraph支持 | 向后兼容 | 说明 |
|------|--------------|---------|------|
| v2.0+ | ✅ 完全支持 | ✅ 是 | 推荐版本 |
| v1.x | ❌ 不支持 | ✅ 是 | 自动回退到传统系统 |

## 🔗 相关文档

- [统一智能架构集成方案](./统一智能架构集成方案.md)
- [阶段二实施日志](./implementation_logs/phase2_detailed_implementation.md)
- [LangGraph官方文档](https://langchain-ai.github.io/langgraph/)

---

*文档版本: 1.0*  
*最后更新: 2025-05-28 00:43:11*  
*维护者: AI Assistant Team*
